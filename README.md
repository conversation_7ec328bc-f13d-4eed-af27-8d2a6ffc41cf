# deepsearch

代理

```

# 导入翻墙代理
export http_proxy=http://***********:11080 && export https_proxy=http://***********:11080 && export HTTP_PROXY=http://***********:11080 && export HTTPS_PROXY=http://***********:11080 && export no_proxy="localhost,127.0.0.1,localaddress,localdomain.com,internal,corp.kuaishou.com,test.gifshow.com,staging.kuaishou.com" && export NO_PROXY="localhost,127.0.0.1,localaddress,localdomain.com,internal,corp.kuaishou.com,test.gifshow.com,staging.kuaishou.com"

```


```

# 检查 .bashrc 是否有配置
grep "NVM_DIR" ~/.bashrc

# 如果没有，重新添加
echo 'export NVM_DIR="$HOME/.nvm"' >> ~/.bashrc
echo '[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"' >> ~/.bashrc
source ~/.bashrc

```

```
# 查看当前 PATH
echo $PATH

# 确保 nvm 的路径在最前面
export PATH="$NVM_DIR/versions/node/v18.19.0/bin:$PATH"


```